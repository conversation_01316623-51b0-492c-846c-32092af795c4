{"name": "infrachain-dao", "version": "0.1.0", "private": true, "dependencies": {"@azure/storage-blob": "^12.27.0", "@headlessui/react": "^2.2.4", "@radix-ui/react-icons": "^1.3.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "ether": "^0.0.9", "ethers": "^6.15.0", "framer-motion": "^12.19.2", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "tailwind-variants": "^1.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "css-loader": "^7.1.2", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "tailwindcss": "^4.1.11"}}